<template>
  <div>
    <!-- Hero Section -->
    <HeroSection
      v-if="heroSectionData"
      :backgroundImageUrl="heroSectionData.backgroundImageUrl"
      :headline="heroSectionData.headline"
      :subheadline="heroSectionData.subheadline"
      :primaryButtonText="heroSectionData.primaryButtonText"
      :primaryButtonLink="heroSectionData.primaryButtonLink"
      :primaryButtonWhatsapp="heroSectionData.primaryButtonWhatsapp"
      :secondaryButtonText="heroSectionData.secondaryButtonText"
      :secondaryButtonLink="heroSectionData.secondaryButtonLink"
    />
    
    <!-- Why Choose Section -->
    <WhyChooseSection
      v-if="whyChooseData"
      :heading="whyChooseData.heading"
      :subheading="whyChooseData.subheading"
      :features="whyChooseData.features"
    />
    
    <!-- Services Section -->
    <ServicesGridSection
      v-if="servicesSectionData"
      sectionId="services"
      badgeText="<PERSON>ana<PERSON>"
      :heading="servicesSectionData.heading"
      :subheading="servicesSectionData.subheading"
      :services="servicesSectionData.services"
      buttonText="Pelajari Lebih Lanjut"
    />

    <!-- Process Section -->
    <ProcessStepsSection
      v-if="processSectionData"
      badgeText="Proses Kerja"
      :heading="processSectionData.heading"
      :subheading="processSectionData.subheading"
      :steps="processSectionData.steps"
    />

    <!-- Pricing Section -->
    <PricingPlansSection
      v-if="pricingSectionData"
      badgeText="Harga Layanan"
      :heading="pricingSectionData.heading"
      :subheading="pricingSectionData.subheading"
      :plans="pricingSectionData.plans"
      buttonText="Pilih Paket"
    />

    <!-- Testimonials Section -->
    <TestimonialsSection
      v-if="testimonialsSectionData"
      :heading="testimonialsSectionData.heading"
      :subheading="testimonialsSectionData.subheading"
      :testimonials="testimonialsSectionData.testimonials"
    />

    <!-- FAQ Section -->
    <FaqSection
      v-if="faqSectionData"
      :heading="faqSectionData.heading"
      :subheading="faqSectionData.subheading"
      :faqs="faqSectionData.faqs"
    />

    <!-- CTA Section -->
      <CtaContactSection 
      v-if="ctaSectionData"
      :heading="ctaSectionData.heading"
      :subheading="ctaSectionData.subheading"
      :benefits="ctaSectionData.benefits"
      :button-text="ctaSectionData.primaryButtonText"
      whatsapp-link="https://wa.me/6281234567890?text=Halo,%20saya%20tertarik%20dengan%20layanan%20Pengurusan%20Perijinan%20Usaha"
      :form-heading="ctaSectionData.formHeading"
      :operational-hours="ctaSectionData.operationalHours"
      :contact-info="ctaSectionData.contactInfo"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import HeroSection from '~/components/HeroSection.vue'
import WhyChooseSection from '~/components/WhyChooseSection.vue'
import ServicesGridSection from '~/components/ServicesGridSection.vue'
import ProcessStepsSection from '~/components/ProcessStepsSection.vue'
import PricingPlansSection from '~/components/PricingPlansSection.vue'
import TestimonialsSection from '~/components/TestimonialsSection.vue'
import FaqSection from '~/components/FaqSection.vue'
import CtaContactSection from '~/components/CtaContactSection.vue'
import AppButton from '~/components/atoms/AppButton.vue'

// Load data from API
const { data: perijinanData } = await useFetch('/api/perijinan')

// For debugging
console.log('API Data:', perijinanData.value)

// Use API data directly
const pageData = computed(() => perijinanData.value || {})

// Extract section data
const heroSectionData = computed(() => pageData.value.heroSection)
const whyChooseData = computed(() => pageData.value.whyChoose)
const servicesSectionData = computed(() => pageData.value.servicesSection)
const processSectionData = computed(() => pageData.value.processSection)
const pricingSectionData = computed(() => pageData.value.pricingSection)
const testimonialsSectionData = computed(() => pageData.value.testimonialsSection)
const faqSectionData = computed(() => pageData.value.faqSection)
const ctaSectionData = computed(() => pageData.value.ctaSection)

useHead({
  title: 'Jasa Pengurusan Perijinan Usaha | Gemilang VO',
  meta: [
    { name: 'description', content: 'Layanan pengurusan perijinan usaha profesional. Proses cepat, legal, dan terpercaya dengan pendampingan penuh dari tim ahli.' }
  ]
})
</script>