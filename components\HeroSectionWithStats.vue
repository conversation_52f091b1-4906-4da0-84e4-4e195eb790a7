<template>
  <div class="relative">
    <!-- Hero Background -->
    <div class="absolute inset-0 z-0">
      <img 
        :src="props.backgroundImageUrl"
        alt="Virtual Office"
        class="w-full h-full object-cover"
      />
      <div class="absolute inset-0 bg-gradient-to-r from-primary-900/90 to-black/70"></div>
    </div>

    <!-- Hero Content -->
    <div class="relative z-10 min-h-[90vh] flex items-center">
      <div class="container mx-auto px-4 py-20">
        <div class="max-w-2xl animate-slide-up">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            {{ props.headline }}
          </h1>
          <p class="text-xl text-gray-200 mb-8">
            {{ props.subheadline }}
          </p>
          <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <AppButton
              size="lg"
              :href="props.primaryButtonLink"
              :whatsapp="props.primaryButtonWhatsapp"
            >
              {{ props.primaryButtonText }}
            </AppButton>
            <NuxtLink :to="props.secondaryButtonLink">
              <AppButton size="lg" variant="outline" class="text-white border-white hover:bg-white/10 w-full sm:w-auto">
                {{ props.secondaryButtonText }}
              </AppButton>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Section -->
    <div v-if="props.stats && props.stats.length > 0" class="absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-md py-8 z-10">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div v-for="(stat, index) in props.stats" :key="index" class="text-center">
            <div class="text-4xl font-bold text-white mb-2">{{ stat.value }}</div>
            <div class="text-gray-300">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Wave Separator -->
    <div class="absolute bottom-0 left-0 right-0">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" class="fill-white w-full h-auto">
        <path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
      </svg>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  backgroundImageUrl: {
    type: String,
    default: 'https://images.pexels.com/photos/380768/pexels-photo-380768.jpeg'
  },
  headline: {
    type: String,
    default: 'Premium Virtual Office Solutions'
  },
  subheadline: {
    type: String,
    default: 'Professional business addresses and support services across major Indonesian cities'
  },
  primaryButtonText: {
    type: String,
    default: 'Konsultasi Virtual Office'
  },
  primaryButtonLink: {
    type: String,
    default: 'https://wa.me/6281234567890?text=Halo,%20saya%20tertarik%20dengan%20layanan%20Virtual%20Office'
  },
  primaryButtonWhatsapp: {
    type: Boolean,
    default: true
  },
  secondaryButtonText: {
    type: String,
    default: 'Lihat Layanan Kami'
  },
  secondaryButtonLink: {
    type: String,
    default: '/services'
  },
  stats: {
    type: Array,
    default: () => []
  }
})
</script>