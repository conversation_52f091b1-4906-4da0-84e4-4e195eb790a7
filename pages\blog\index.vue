<template>
  <div>
    <!-- Blog Hero -->
    <div class="relative">
      <div class="absolute inset-0 z-0">
        <img 
          src="https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg" 
          alt="Blog" 
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-r from-primary-900/90 to-black/70"></div>
      </div>
      
      <div class="relative z-10 pt-32 pb-20">
        <div class="container mx-auto px-4">
          <div class="max-w-3xl animate-slide-up">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
              Blog & Artikel
            </h1>
            <p class="text-xl text-gray-200">
              Informasi dan tips seputar bisnis dan virtual office
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Blog Content -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <!-- Categories -->
        <div class="flex flex-wrap gap-2 mb-8">
          <NuxtLink 
            v-for="category in categories" 
            :key="category"
            :to="`/blog/category/${category}`"
            class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200"
            :class="[
              currentCategory === category 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
          >
            {{ category }}
          </NuxtLink>
        </div>

        <!-- Articles Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <article v-for="article in articles" :key="article._path" class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <NuxtLink :to="article._path">
              <img 
                :src="article.image" 
                :alt="article.title"
                class="w-full h-48 object-cover"
              />
              <div class="p-6">
                <div class="flex items-center gap-2 mb-3">
                  <span 
                    v-for="tag in article.tags" 
                    :key="tag"
                    class="text-xs font-medium px-2 py-1 rounded-full bg-primary-50 text-primary-700"
                  >
                    {{ tag }}
                  </span>
                </div>
                <h2 class="text-xl font-bold mb-2 line-clamp-2 hover:text-primary-600 transition-colors duration-200">
                  {{ article.title }}
                </h2>
                <p class="text-gray-600 mb-4 line-clamp-3">
                  {{ article.description }}
                </p>
                <div class="flex items-center justify-between text-sm text-gray-500">
                  <span>{{ formatDate(article.date) }}</span>
                  <span class="flex items-center">
                    <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />
                    {{ article.readingTime }} min read
                  </span>
                </div>
              </div>
            </NuxtLink>
          </article>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="mt-12 flex justify-center gap-2">
          <button 
            v-for="page in totalPages" 
            :key="page"
            @click="currentPage = page"
            class="w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200"
            :class="[
              currentPage === page 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
          >
            {{ page }}
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
const { data: articles } = await useAsyncData('articles', () => queryContent('/blog')
  .sort({ date: -1 })
  .find()
)

const categories = computed(() => {
  const allCategories = articles.value?.map(article => article.category) || []
  return ['All', ...new Set(allCategories)].filter(Boolean)
})

const currentCategory = ref('All')
const currentPage = ref(1)
const articlesPerPage = 9
const totalPages = computed(() => Math.ceil((articles.value?.length || 0) / articlesPerPage))

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

useHead({
  title: 'Blog & Artikel | Gemilang VO',
  meta: [
    { name: 'description', content: 'Baca artikel dan tips terbaru seputar bisnis, virtual office, dan perkembangan dunia usaha di Indonesia.' }
  ]
})
</script>