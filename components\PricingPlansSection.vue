<template>
  <section class="py-20" :class="bgClass">
    <div class="container mx-auto px-4">
      <div class="max-w-3xl mx-auto text-center mb-16">
        <span class="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
          {{ badgeText }}
        </span>
        <h2 class="text-3xl md:text-4xl font-bold mb-6">{{ heading }}</h2>
        <p class="text-gray-600 text-lg">
          {{ subheading }}
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
        <div v-for="(plan, index) in plans" :key="index" 
            class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300"
            :class="{'border-2 border-primary-500': plan.popular}">
          <div class="p-8" :class="{'bg-primary-500 text-white': plan.popular}">
            <h3 class="text-2xl font-bold mb-2">{{ plan.name }}</h3>
            <p class="opacity-90 mb-4">{{ plan.description }}</p>
            <div class="text-3xl font-bold mb-2">{{ plan.price }}</div>
            <p class="text-sm opacity-75">{{ plan.period }}</p>
          </div>
          <div class="p-8">
            <ul class="space-y-4 mb-8">
              <li v-for="feature in plan.features" :key="feature" class="flex items-start">
                <Icon name="heroicons:check-circle" class="text-primary-500 w-5 h-5 mr-3 mt-0.5" />
                <span>{{ feature }}</span>
              </li>
            </ul>
            <AppButton 
              :variant="plan.popular ? 'primary' : 'outline'"
              class="w-full"
            >
              {{ buttonText }}
            </AppButton>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import AppButton from '~/components/atoms/AppButton.vue'

defineProps({
  badgeText: {
    type: String,
    default: 'Harga Layanan'
  },
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    default: ''
  },
  plans: {
    type: Array,
    required: true,
    default: () => []
  },
  buttonText: {
    type: String,
    default: 'Pilih Paket'
  },
  bgClass: {
    type: String,
    default: ''
  }
})
</script>