<template>
  <footer class=" text-black py-12">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <h3 class="text-xl font-bold mb-4">Gemilang VO</h3>
          <p class="text-gray-800 mb-4">Premium virtual office solutions for modern businesses across Indonesia.</p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-800 hover:text-primary-500">
              <Icon name="mdi:facebook" size="24" />
            </a>
            <a href="#" class="text-gray-800 hover:text-primary-500">
              <Icon name="mdi:twitter" size="24" />
            </a>
            <a href="#" class="text-gray-800 hover:text-primary-500">
              <Icon name="mdi:instagram" size="24" />
            </a>
            <a href="#" class="text-gray-800 hover:text-primary-500">
              <Icon name="mdi:linkedin" size="24" />
            </a>
          </div>
        </div>
        
        <div>
          <h4 class="text-lg font-semibold mb-4">Locations</h4>
          <ul class="space-y-2">
            <li v-for="city in cities" :key="city.id">
              <NuxtLink :to="`/cities/${city.id}`" class="text-gray-800 hover:text-primary-500">
                {{ city.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>
        
        <div>
          <h4 class="text-lg font-semibold mb-4">Services</h4>
          <ul class="space-y-2">
            <li v-for="service in services" :key="service.id">
              <NuxtLink :to="service.path" class="text-gray-800 hover:text-primary-500">
                {{ service.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>
        
        <div>
          <h4 class="text-lg font-semibold mb-4">Contact Us</h4>
          <ul class="space-y-2 text-gray-800">
            <li class="flex items-start">
              <Icon name="heroicons:map-pin" class="mr-2 mt-1" />
              <span>Jl. Gatot Subroto No. 123<br>Jakarta, Indonesia</span>
            </li>
            <li class="flex items-center">
              <Icon name="heroicons:phone" class="mr-2" />
              <span>+62 21 1234 5678</span>
            </li>
            <li class="flex items-center">
              <Icon name="heroicons:envelope" class="mr-2" />
              <span>info@Gemilang VO.id</span>
            </li>
          </ul>
        </div>
      </div>
      
      <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between">
        <p class="text-gray-800">© {{ new Date().getFullYear() }} Gemilang VO. All rights reserved.</p>
        <div class="mt-4 md:mt-0">
          <NuxtLink to="/privacy" class="text-gray-800 hover:text-primary-500 mr-4">Privacy Policy</NuxtLink>
          <NuxtLink to="/terms" class="text-gray-800 hover:text-primary-500">Terms of Service</NuxtLink>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
const { data: citiesData } = await useFetch('/api/cities')
const { data: servicesData } = await useFetch('/api/services')

const cities = computed(() => citiesData.value?.cities || [])
const services = computed(() => servicesData.value?.services || [])
</script>