<template>
  <NuxtLink :to="service.path" class="block group">
    <div class="bg-white rounded-xl overflow-hidden shadow hover:shadow-lg transition-all duration-300">
      <div class="relative h-48">
        <img 
          :src="service.image || 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg'" 
          :alt="service.name"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        <div class="absolute bottom-0 left-0 p-6">
          <h3 class="text-xl font-bold text-white mb-2">{{ service.name }}</h3>
          <p class="text-white/90 text-sm line-clamp-2">{{ service.description }}</p>
        </div>
      </div>
      <div class="p-4">
        <button class="w-full text-primary-600 font-medium py-2 px-4 rounded-lg border border-primary-600 hover:bg-primary-50 transition-colors duration-200 flex items-center justify-center">
          {{ service.buttonText || 'Pelajari Lebih <PERSON>n<PERSON>t' }}
          <Icon name="heroicons:arrow-right" class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
        </button>
      </div>
    </div>
  </NuxtLink>
</template>

<script setup>
defineProps({
  service: {
    type: Object,
    required: true
  }
})
</script>