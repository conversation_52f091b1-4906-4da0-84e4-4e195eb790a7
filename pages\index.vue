<template>
  <div>
    <!-- Loading state -->
    <div v-if="isLoading" class="py-20 text-center">
      <h2 class="text-2xl font-bold mb-4">Loading content...</h2>
      <p class="text-gray-600">Please wait while we prepare your experience</p>
    </div>

    <!-- Error state -->
    <div v-else-if="hasError" class="py-20 text-center bg-red-50">
      <h2 class="text-2xl font-bold text-red-600 mb-4">We're experiencing technical difficulties</h2>
      <p class="text-gray-700 mb-4">We're sorry, but we couldn't load the content. Please try refreshing the page.</p>
      <p class="text-sm text-gray-500 mb-6">Error details: {{ errorMessage }}</p>
      <button @click="retryLoading" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
        Retry Loading
      </button>
    </div>

    <!-- Content when loaded successfully -->
    <template v-else>
      <HeroSection
        v-if="heroSectionData"
        :backgroundImageUrl="heroSectionData.backgroundImageUrl"
        :headline="heroSectionData.headline"
        :subheadline="heroSectionData.subheadline"
        :primaryButtonText="heroSectionData.primaryButtonText"
        :primaryButtonLink="heroSectionData.primaryButtonLink"
        :primaryButtonWhatsapp="heroSectionData.primaryButtonWhatsapp"
        :secondaryButtonText="heroSectionData.secondaryButtonText"
        :secondaryButtonLink="heroSectionData.secondaryButtonLink"
      />
      
      <!-- Why Choose Section -->
      <WhyChooseSection
        v-if="whyChooseData"
        :heading="whyChooseData.heading"
        :subheading="whyChooseData.subheading"
        :features="whyChooseData.features"
      />
      
      <!-- Office Locations Section -->
      <OfficeLocationsSection
        v-if="officeLocationsData && cities.length > 0"
        :heading="officeLocationsData.heading"
        :subheading="officeLocationsData.subheading"
        :cities="cities"
      />
      
      <!-- Services Section -->
      <ServicesSection
        v-if="servicesDataCmp && services.length > 0"
        :heading="servicesDataCmp.heading"
        :subheading="servicesDataCmp.subheading"
        :services="services"
      />
      
      <!-- New CTA Section (before testimonials) -->
      <NewCta
        v-if="newCtaData"
        :subtitle="newCtaData.subtitle"
        :heading="newCtaData.heading"
        :subheading="newCtaData.subheading"
        :salesIcon="newCtaData.salesIcon"
        :salesTitle="newCtaData.salesTitle"
        :salesDescription="newCtaData.salesDescription"
        :supportIcon="newCtaData.supportIcon"
        :supportTitle="newCtaData.supportTitle"
        :supportDescription="newCtaData.supportDescription"
        :supportDescriptionMiddle="newCtaData.supportDescriptionMiddle"
        :whatsappNumber="newCtaData.whatsappNumber"
        :emailAddress="newCtaData.emailAddress"
        :buttonText="newCtaData.buttonText"
        :imageUrl="newCtaData.imageUrl"
        :imageAlt="newCtaData.imageAlt"
      />
      
      <!-- Testimonials Section -->
      <TestimonialsSection
        v-if="testimonialsData"
        :heading="testimonialsData.heading"
        :subheading="testimonialsData.subheading"
        :testimonials="testimonialsData.testimonials"
      />
      
      <!-- FAQ Section -->
      <FaqSection
        v-if="faqData"
        :heading="faqData.heading"
        :subheading="faqData.subheading"
        :faqs="faqData.faqs"
      />
    </template>
  </div>
</template>

<script setup>
import NewCta from '~/components/NewCta.vue'
import { ref } from 'vue'

// Track loading and error states
const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')

// Fetch data with better error handling
const fetchData = async () => {
  isLoading.value = true
  hasError.value = false
  errorMessage.value = ''
  
  try {
    console.log('Fetching homepage data...')
    const [homeResponse, citiesResponse, servicesResponse] = await Promise.all([
      useFetch('/api/home'),
      useFetch('/api/cities'),
      useFetch('/api/services')
    ])
    
    homePageData.value = homeResponse.data.value
    citiesData.value = citiesResponse.data.value
    servicesData.value = servicesResponse.data.value
    
    // Check if we got valid data
    if (!homePageData.value) {
      throw new Error('Home page data is empty or invalid')
    }
    
    console.log('Data fetched successfully:', {
      homeData: !!homePageData.value,
      citiesData: !!citiesData.value,
      servicesData: !!servicesData.value
    })
  } catch (error) {
    console.error('Error fetching data:', error)
    hasError.value = true
    errorMessage.value = error.message || 'Unknown error occurred'
  } finally {
    isLoading.value = false
  }
}

// Function to retry loading data
const retryLoading = () => {
  fetchData()
}

// Data references
const homePageData = ref(null)
const citiesData = ref(null)
const servicesData = ref(null)

// Computed properties for components
const cities = computed(() => citiesData.value?.cities || [])
const services = computed(() => servicesData.value?.services || [])

const heroSectionData = computed(() => homePageData.value?.heroSection)
const whyChooseData = computed(() => homePageData.value?.whyChoose)
const officeLocationsData = computed(() => homePageData.value?.officeLocationsSection)
const servicesDataCmp = computed(() => homePageData.value?.servicesSection)
const faqData = computed(() => homePageData.value?.faqSection)
const testimonialsData = computed(() => homePageData.value?.testimonialsSection)
const ctaData = computed(() => homePageData.value?.ctaSection)
const newCtaData = computed(() => homePageData.value?.newCta)

// Initial data fetch
fetchData()

useHead({
  title: 'Virtual Office Indonesia | Solusi Kantor Modern',
  meta: [
    { name: 'description', content: 'Layanan virtual office premium di lokasi strategis. Tingkatkan profesionalitas bisnis Anda dengan alamat kantor prestisius dan layanan lengkap.' }
  ]
})
</script>