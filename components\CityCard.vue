<template>
  <NuxtLink 
    :to="`/cities/${city.id}`"
    class="group bg-white rounded-xl overflow-hidden shadow-sm transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1"
  >
    <div class="relative h-56 overflow-hidden">
      <img 
        :src="city.image" 
        :alt="`Virtual Office in ${city.name}`" 
        class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
      <div class="absolute bottom-0 left-0 p-6 w-full">
        <h2 class="text-2xl font-bold text-white mb-2">Virtual Office in {{ city.name }}</h2>
        <div class="flex items-center text-white/90">
          <Icon name="heroicons:map-pin" class="w-4 h-4 mr-2" />
          <span class="text-sm">Premium Business District</span>
        </div>
      </div>
    </div>
    <div class="p-6">
      <p class="text-gray-600 mb-4 line-clamp-2">{{ city.description }}</p>
      <div class="space-y-3 mb-4">
        <div class="flex items-center text-gray-700">
          <Icon name="heroicons:building-office-2" class="w-4 h-4 mr-2" />
          <span class="text-sm">Professional Business Address</span>
        </div>
        <div class="flex items-center text-gray-700">
          <Icon name="heroicons:phone" class="w-4 h-4 mr-2" />
          <span class="text-sm">Dedicated Reception</span>
        </div>
        <div class="flex items-center text-gray-700">
          <Icon name="heroicons:wifi" class="w-4 h-4 mr-2" />
          <span class="text-sm">High-Speed Internet</span>
        </div>
      </div>
      <div class="flex justify-between items-center pt-4 border-t border-gray-100">
        <div>
          <p class="text-sm text-gray-500">Starting from</p>
          <p class="text-lg font-bold text-primary-600">{{ city.prices.monthly }}<span class="text-sm font-normal">/month</span></p>
        </div>
        <span class="text-primary-600 group-hover:translate-x-1 transition-transform duration-300">
          <Icon name="heroicons:arrow-right" size="24" />
        </span>
      </div>
    </div>
  </NuxtLink>
</template>

<script setup>
defineProps({
  city: {
    type: Object,
    required: true
  }
})
</script>