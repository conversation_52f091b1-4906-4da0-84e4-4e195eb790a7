<template>
  <section v-if="locationInfo" class="py-16">
    <div class="container mx-auto px-4">
      <div class="bg-white rounded-xl shadow-md overflow-hidden p-8">
        <h2 v-if="locationInfo.heading" class="text-2xl font-bold mb-4">{{ locationInfo.heading }}</h2>
        <p v-if="locationInfo.address" class="text-gray-600 mb-6">{{ locationInfo.address }}</p>

        <div class="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Image Slider -->
          <div v-if="roomImages && roomImages.length" class="mb-6">
            <h3 class="text-lg font-semibold mb-3"><PERSON><PERSON></h3>
            <div class="relative">
              <div class="image-slider overflow-hidden rounded-lg">
                <div class="slider-container flex transition-transform duration-300" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                  <div v-for="(image, index) in roomImages" :key="index" class="slider-slide w-full flex-shrink-0">
                    <img :src="image.url" :alt="image.alt || 'Room image'" class="w-full h-64 object-cover rounded-lg">
                  </div>
                </div>
              </div>

              <!-- Navigation Arrows -->
              <button
                @click="prevSlide"
                class="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-md z-10"
                :disabled="currentSlide === 0"
                :class="{ 'opacity-50 cursor-not-allowed': currentSlide === 0 }"
              >
                <Icon name="heroicons:chevron-left" size="20" />
              </button>

              <button
                @click="nextSlide"
                class="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-md z-10"
                :disabled="currentSlide === roomImages.length - 1"
                :class="{ 'opacity-50 cursor-not-allowed': currentSlide === roomImages.length - 1 }"
              >
                <Icon name="heroicons:chevron-right" size="20" />
              </button>

              <!-- Dots Indicator -->
              <div class="flex justify-center mt-3 space-x-2">
                <button
                  v-for="(_, index) in roomImages"
                  :key="index"
                  @click="goToSlide(index)"
                  class="w-2.5 h-2.5 rounded-full transition-colors duration-200"
                  :class="currentSlide === index ? 'bg-primary-600' : 'bg-gray-300 hover:bg-gray-400'"
                ></button>
              </div>
            </div>
          </div>

          <!-- Facilities -->
          <div v-if="locationInfo.facilities && locationInfo.facilities.length" class="mb-6">
            <h3 v-if="locationInfo.facilitiesHeading" class="text-lg font-semibold mb-3">{{ locationInfo.facilitiesHeading }}</h3>
            <ul class="space-y-2">
              <li v-for="facility in locationInfo.facilities" :key="facility" class="flex items-start">
                <Icon name="heroicons:check-circle" class="text-primary-500 mr-2 mt-0.5 flex-shrink-0" />
                <span class="text-sm">{{ facility }}</span>
              </li>
            </ul>
          </div>
        </div>

        <div v-if="locationInfo.mapUrl" class="bg-gray-100 rounded-lg p-4 mt-6">
          <iframe
            :src="locationInfo.mapUrl"
            width="100%"
            height="300"
            style="border:0;"
            allowfullscreen=""
            loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"
            class="rounded-lg"
          ></iframe>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// Assuming AppButton and Icon are globally registered or imported if needed
// import AppButton from '~/components/atoms/AppButton.vue';
import { ref } from 'vue';

const props = defineProps({
  locationInfo: {
    type: Object,
    default: () => ({})
  },
  roomImages: {
    type: Array,
    default: () => []
  }
});

// Image slider logic
const currentSlide = ref(0);

const nextSlide = () => {
  if (currentSlide.value < props.roomImages.length - 1) {
    currentSlide.value++;
  }
};

const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  }
};

const goToSlide = (index) => {
  currentSlide.value = index;
};
</script>