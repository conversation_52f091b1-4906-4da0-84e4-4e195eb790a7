<template>
  <section class="py-16 bg-blue-50" aria-labelledby="new-cta-heading">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row items-center justify-between">
        <!-- Left content -->
        <div class="w-full md:w-1/2 mb-10 md:mb-0 pr-0 md:pr-8">
          <p class="text-blue-600 font-medium mb-4">{{ subtitle }}</p>
          <h2 id="new-cta-heading" class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {{ heading }}
          </h2>
          <p class="text-gray-600 mb-8">{{ subheading }}</p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <!-- Sales Contact -->
            <div>
              <div class="flex items-center mb-4">
                <div class="bg-blue-600 rounded-full p-3 mr-3">
                  <Icon :name="salesIcon" class="w-6 h-6 text-white" />
                </div>
                <h3 class="text-xl font-semibold">{{ salesTitle }}</h3>
              </div>
              <p class="text-gray-600">{{ salesDescription }}</p>
            </div>
            
            <!-- Support Contact -->
            <div>
              <div class="flex items-center mb-4">
                <div class="bg-blue-600 rounded-full p-3 mr-3">
                  <Icon :name="supportIcon" class="w-6 h-6 text-white" />
                </div>
                <h3 class="text-xl font-semibold">{{ supportTitle }}</h3>
              </div>
              <p class="text-gray-600">
                {{ supportDescription }}
                <a :href="`https://wa.me/${whatsappNumber}`" class="text-blue-600 hover:underline">+{{ whatsappNumber }}</a>
                {{ supportDescriptionMiddle }}
                <a :href="`mailto:${emailAddress}`" class="text-blue-600 hover:underline">{{ emailAddress }}</a>
              </p>
            </div>
          </div>
          
          <a :href="`https://wa.me/${whatsappNumber}`" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-300">
            {{ buttonText }}
            <Icon name="heroicons:arrow-right" class="ml-2 h-5 w-5" />
          </a>
        </div>
        
        <!-- Right image -->
        <div class="w-full md:w-1/2 flex items-center justify-center">
          <img
            :src="imageUrl"
            :alt="imageAlt"
            class="rounded-lg shadow-lg w-full max-w-md max-h-96 object-cover object-center"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({
  subtitle: {
    type: String,
    required: true
  },
  heading: {
    type: String,
    required: true
  },
  subheading: {
    type: String,
    required: true
  },
  salesIcon: {
    type: String,
    default: 'heroicons:user-circle'
  },
  salesTitle: {
    type: String,
    required: true
  },
  salesDescription: {
    type: String,
    required: true
  },
  supportIcon: {
    type: String,
    default: 'heroicons:chat-bubble-left-right'
  },
  supportTitle: {
    type: String,
    required: true
  },
  supportDescription: {
    type: String,
    default: ''
  },
  supportDescriptionMiddle: {
    type: String,
    default: ' atau melalui email di '
  },
  whatsappNumber: {
    type: String,
    required: true
  },
  emailAddress: {
    type: String,
    required: true
  },
  buttonText: {
    type: String,
    required: true
  },
  imageUrl: {
    type: String,
    required: true
  },
  imageAlt: {
    type: String,
    default: 'Customer Support'
  }
})
</script>