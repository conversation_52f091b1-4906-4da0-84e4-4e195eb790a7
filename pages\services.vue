<template>
  <div>
    <!-- Services Hero -->
    <div class="relative">
      <div class="absolute inset-0 z-0">
        <img 
          src="https://images.pexels.com/photos/416405/pexels-photo-416405.jpeg" 
          alt="Our Services" 
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-r from-primary-900/90 to-black/70"></div>
      </div>
      
      <div class="relative z-10 pt-32 pb-20">
        <div class="container mx-auto px-4">
          <div class="max-w-3xl animate-slide-up">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
              Our Services
            </h1>
            <p class="text-xl text-gray-200 mb-6">
              Comprehensive business support solutions to help your business grow
            </p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Services Grid -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <ServiceCard v-for="service in services" :key="service.id" :service="service" />
        </div>
      </div>
    </section>
    
    <!-- Process Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <SectionHeading 
          title="How It Works"
          subtitle="A simple, transparent process to get started with our services"
        />
        
        <div class="relative">
          <!-- Timeline Line -->
          <div class="hidden md:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-primary-200 transform -translate-x-1/2"></div>
          
          <div class="space-y-12 relative">
            <div v-for="(step, index) in process" :key="index" class="md:flex items-center">
              <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0" :class="{ 'md:text-right md:order-1': index % 2 === 0, 'md:pl-12 md:pr-0': index % 2 !== 0 }">
                <h3 class="text-xl font-bold mb-2">{{ step.title }}</h3>
                <p class="text-gray-600">{{ step.description }}</p>
              </div>
              
              <div class="flex md:absolute md:left-1/2 md:transform md:-translate-x-1/2">
                <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold z-10">
                  {{ index + 1 }}
                </div>
              </div>
              
              <div class="md:w-1/2 md:pl-12" :class="{ 'md:order-2': index % 2 === 0, 'md:pr-12 md:pl-0': index % 2 !== 0 }">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                  <div class="flex items-start">
                    <Icon :name="step.icon" class="text-primary-500 mr-3 mt-0.5" />
                    <p class="text-gray-700">{{ step.detail }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- CTA Section -->
    <section class="py-16">
      <div class="container mx-auto px-4 max-w-3xl">
        <div class="bg-gradient-to-r from-primary-600 to-primary-800 rounded-2xl shadow-xl p-10 text-white">
          <h2 class="text-3xl font-bold mb-4 text-center">Ready to Get Started?</h2>
          <p class="text-xl mb-8 text-center text-primary-100">Contact us today to discuss how our services can benefit your business</p>
          
          <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <AppButton size="lg" variant="outline" class="text-white border-white hover:bg-white/10">
              Schedule a Consultation
            </AppButton>
            <AppButton size="lg" variant="secondary">
              View Pricing
            </AppButton>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
const { data: servicesData } = await useFetch('/api/services')
const services = computed(() => servicesData.value?.services || [])

const process = [
  {
    title: "Initial Consultation",
    description: "We'll discuss your business needs and recommend the best solution for you",
    icon: "heroicons:chat-bubble-left-right",
    detail: "A personalized 30-minute session with our business advisor to understand your requirements"
  },
  {
    title: "Choose Your Plan",
    description: "Select from our range of flexible plans and add-on services",
    icon: "heroicons:clipboard-document-list",
    detail: "Our advisor will help you select the perfect plan based on your business needs and budget"
  },
  {
    title: "Setup & Onboarding",
    description: "We'll handle all the paperwork and setup for a smooth transition",
    icon: "heroicons:document-check",
    detail: "Quick and efficient setup process with minimum disruption to your business operations"
  },
  {
    title: "Start Using Your Services",
    description: "Begin receiving mail, using meeting rooms, and accessing all included services",
    icon: "heroicons:rocket-launch",
    detail: "Full access to your virtual office services within 24-48 hours of completing registration"
  }
]

useHead({
  title: 'Our Services | Gemilang VO',
  meta: [
    { name: 'description', content: 'Comprehensive business support services including business permit processing, company formation, meeting room rental, and virtual assistant services.' }
  ]
})
</script>