// Import the JSON file directly
import servicesData from '~/data/services.json';

export default defineEventHandler(async (event) => {
  try {
    console.log('API Services Data loaded successfully');
    return servicesData;
  } catch (error) {
    console.error('Error loading services data:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error: Could not load services data',
    });
  }
});