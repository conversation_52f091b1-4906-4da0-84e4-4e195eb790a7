<template>
  <section v-if="pricingPlans && pricingPlans.plans" class="py-16">
    <div class="container mx-auto px-4">
      <h2 v-if="pricingPlans.heading" class="text-3xl font-bold text-center mb-12">{{ pricingPlans.heading }}</h2>
      
      <!-- Pricing Table -->
      <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full min-w-[800px]">
            <!-- Header Row -->
            <thead>
              <tr class="bg-gradient-to-r from-purple-600 to-blue-600">
                <!-- Package Type Header -->
                <th class="p-4 md:p-6 text-center min-w-[250px]">
                  <div class="bg-orange-500 text-white px-4 py-3 rounded-xl font-bold text-sm md:text-lg">
                    JENIS<br>PAKET
                  </div>
                </th>

                <!-- Plan Headers -->
                <th v-for="(plan, index) in pricingPlans.plans" :key="index" class="p-4 md:p-6 text-center min-w-[180px]">
                  <div :class="[
                    'px-3 py-3 rounded-xl font-bold text-white text-sm md:text-lg',
                    getPlanHeaderColor(index)
                  ]">
                    <div class="text-xs md:text-sm mb-1">{{ getPlanDisplayName(plan.name) }}</div>
                    <div class="text-lg md:text-2xl font-bold">{{ formatPrice(plan.priceMonthly || plan.priceYearly || plan.priceQuarterly) }}</div>
                    <div class="text-xs opacity-90">{{ plan.priceMonthlySuffix || plan.priceYearlySuffix || plan.priceQuarterlySuffix }}</div>
                  </div>
                </th>
              </tr>
            </thead>

            <!-- Features Rows -->
            <tbody class="divide-y divide-gray-200">
              <tr v-for="(feature, featureIndex) in features" :key="featureIndex"
                  :class="featureIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'">

                <!-- Feature Name -->
                <td class="p-3 md:p-4 font-medium text-gray-900 border-r border-gray-200 text-xs md:text-sm">
                  {{ feature.name }}
                </td>

                <!-- Feature Values for each plan -->
                <td v-for="(plan, planIndex) in pricingPlans.plans" :key="planIndex"
                    class="p-3 md:p-4 text-center border-r border-gray-200 last:border-r-0">

                  <!-- Check/Cross Icon -->
                  <Icon v-if="feature.values[planIndex] === true"
                        name="heroicons:check"
                        class="w-5 h-5 md:w-6 md:h-6 text-green-500 mx-auto" />
                  <Icon v-else-if="feature.values[planIndex] === false"
                        name="heroicons:x-mark"
                        class="w-5 h-5 md:w-6 md:h-6 text-red-500 mx-auto" />
                  <!-- Custom text value -->
                  <span v-else class="text-xs md:text-sm font-medium text-gray-700">
                    {{ feature.values[planIndex] }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- WhatsApp Contact Button -->
      <div class="mt-12 text-center">
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-8 text-white shadow-2xl">
          <h3 class="text-2xl font-bold mb-3">🚀 Butuh Konsultasi?</h3>
          <p class="mb-6 text-lg opacity-90">Hubungi kami sekarang untuk mendapatkan penawaran terbaik dan konsultasi gratis!</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <AppButton
              :href="whatsappLink"
              whatsapp
              variant="secondary"
              size="lg"
              class="bg-white text-green-600 hover:bg-gray-100 font-bold px-8 py-4 shadow-lg transform hover:scale-105 transition-all duration-200"
            >
              💬 Chat WhatsApp Sekarang
            </AppButton>
            <div class="text-sm opacity-75">
              📞 Respon cepat dalam 5 menit
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  pricingPlans: {
    type: Object,
    required: true
  },
  cityName: {
    type: String,
    default: ''
  }
})

// WhatsApp link with dynamic city name
const whatsappLink = computed(() => {
  const message = `Halo, saya tertarik dengan layanan Virtual Office di ${props.cityName || 'kota ini'}. Bisakah saya mendapatkan informasi lebih lanjut tentang paket dan harga?`;
  return `https://wa.me/6281234567890?text=${encodeURIComponent(message)}`;
});

// Get header color based on plan index
const getPlanHeaderColor = (index) => {
  const colors = ['bg-blue-500', 'bg-yellow-500', 'bg-teal-500']
  return colors[index] || 'bg-gray-500'
}

// Get simplified plan name for display
const getPlanDisplayName = (fullName) => {
  if (fullName.includes('Basic')) return 'GESIT'
  if (fullName.includes('PT')) return 'TERAMPIL'
  if (fullName.includes('CV')) return 'HEBAT'
  return fullName.split(' ')[0].toUpperCase()
}

// Format price for better display
const formatPrice = (price) => {
  if (!price) return ''
  // Remove 'IDR' and format the number
  return price.replace('IDR', 'Rp').replace(/\s+/g, ' ')
}

// Define features comparison table - dynamically adjust based on number of plans
const features = computed(() => {
  const planCount = props.pricingPlans?.plans?.length || 3;

  // Base features that are common
  const baseFeatures = [
    {
      name: 'LOKASI KANTOR STRATEGIS KE PUSAT KOTA',
      values: Array(planCount).fill(true)
    },
    {
      name: 'NO. TELEPON KANTOR BERSAMA',
      values: Array(planCount).fill(true)
    },
    {
      name: 'CALL CENTRE',
      values: Array(planCount).fill(true)
    },
    {
      name: 'SURAT KONTRAK SEWA MENYEWA',
      values: Array(planCount).fill(true)
    },
    {
      name: 'HANDLING TELEPON',
      values: Array(planCount).fill(true)
    },
    {
      name: 'MENERIMA SURAT DAN PAKET',
      values: Array(planCount).fill(true)
    },
    {
      name: 'NOTIFIKASI PENERIMAAN SURAT DAN PAKET',
      values: Array(planCount).fill(true)
    }
  ];

  // Advanced features - adjust based on plan count
  if (planCount >= 2) {
    baseFeatures.push(
      {
        name: 'LAYANAN RESEPSIONIS',
        values: planCount === 2 ? [false, true] : [false, true, true]
      },
      {
        name: 'MENERIMA TAMU DI KANTOR',
        values: planCount === 2 ? [false, true] : [false, true, true]
      },
      {
        name: 'MENGURUS PERJINAN USAHA',
        values: planCount === 2 ? [false, true] : [false, true, true]
      },
      {
        name: 'BISA UNTUK MENGURUS PKP',
        values: planCount === 2 ? [false, true] : [false, true, true]
      },
      {
        name: 'SURAT KETERANGAN DOMISILI USAHA',
        values: planCount === 2 ? [false, true] : [false, true, true]
      },
      {
        name: 'JASA PENGIRIMAN SURAT',
        values: planCount === 2 ? [false, true] : [false, true, true]
      }
    );
  }

  // Premium features - only for 3+ plans
  if (planCount >= 3) {
    baseFeatures.push(
      {
        name: 'PINJAM IMB UNTUK OSS',
        values: [false, false, true]
      },
      {
        name: 'FORWARD TELEPON KE NO. HP PENYEWA',
        values: [false, false, true]
      },
      {
        name: 'MEETING ROOM',
        values: ['', '1 JAM / HARI', '2 JAM / HARI']
      },
      {
        name: 'FREE COWORKING SPACE ( SOLO, TANGERANG )',
        values: ['', '1 HARI / BULAN', '3 HARI / BULAN']
      },
      {
        name: 'SEWA 2 TAHUN',
        values: ['', 'DISC 1 JUTA', 'DISC 1 JUTA']
      }
    );
  }

  return baseFeatures;
})
</script>
