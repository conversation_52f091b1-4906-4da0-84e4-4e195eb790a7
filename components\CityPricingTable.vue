<template>
  <section v-if="pricingPlans && pricingPlans.plans" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 v-if="pricingPlans.heading" class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{{ pricingPlans.heading }}</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">Pilih paket yang sesuai dengan kebutuhan bisnis Anda. Semua paket sudah termasuk alamat bisnis premium dan layanan profesional.</p>
      </div>

      <!-- Pricing Cards Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <div v-for="(plan, index) in pricingPlans.plans" :key="index"
             class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200 hover:shadow-2xl transition-all duration-300">

          <!-- Plan Header -->
          <div class="bg-primary-600 text-white p-6 text-center">
            <h3 class="text-xl font-bold mb-2">{{ getPlanDisplayName(plan.name) }}</h3>
            <div class="text-3xl font-bold mb-1">{{ formatPrice(plan.priceMonthly || plan.priceYearly || plan.priceQuarterly) }}</div>
            <div class="text-sm opacity-90">{{ plan.priceMonthlySuffix || plan.priceYearlySuffix || plan.priceQuarterlySuffix }}</div>
          </div>

          <!-- Plan Description -->
          <div class="p-6">
            <p class="text-gray-600 mb-6 text-center">{{ plan.description }}</p>

            <!-- Features List -->
            <ul class="space-y-3 mb-8">
              <li v-for="feature in getPlanFeatures(index)" :key="feature" class="flex items-start">
                <Icon name="heroicons:check-circle" class="w-5 h-5 text-primary-500 mr-3 mt-0.5 flex-shrink-0" />
                <span class="text-sm text-gray-700">{{ feature }}</span>
              </li>
            </ul>

            <!-- WhatsApp Button -->
            <div class="text-center">
              <AppButton
                :href="getWhatsappLinkForPlan(plan)"
                whatsapp
                variant="primary"
                size="lg"
                class="w-full font-bold py-3 transition-all duration-200 hover:scale-105"
              >
                Pilih {{ getPlanDisplayName(plan.name) }}
              </AppButton>
            </div>
          </div>
        </div>
      </div>

      <!-- General Contact Section -->
      <div class="mt-12 text-center">
        <div class="bg-primary-600 rounded-xl p-8 text-white shadow-xl">
          <h3 class="text-2xl font-bold mb-3">Butuh Konsultasi Lebih Lanjut?</h3>
          <p class="mb-6 text-lg opacity-90">Tim ahli kami siap membantu Anda memilih paket yang tepat untuk kebutuhan bisnis Anda</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <AppButton
              :href="whatsappLink"
              whatsapp
              variant="secondary"
              size="lg"
              class="bg-white text-primary-600 hover:bg-gray-100 font-bold px-8 py-4 shadow-lg transform hover:scale-105 transition-all duration-200"
            >
              Konsultasi Gratis
            </AppButton>
            <div class="text-sm opacity-75">
              Respon cepat dalam 5 menit
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  pricingPlans: {
    type: Object,
    required: true
  },
  cityName: {
    type: String,
    default: ''
  }
})

// WhatsApp link with dynamic city name for general consultation
const whatsappLink = computed(() => {
  const message = `Halo, saya tertarik dengan layanan Virtual Office di ${props.cityName || 'kota ini'}. Bisakah saya mendapatkan informasi lebih lanjut tentang paket dan harga?`;
  return `https://wa.me/6281234567890?text=${encodeURIComponent(message)}`;
});

// WhatsApp link for specific plan
const getWhatsappLinkForPlan = (plan) => {
  const planName = getPlanDisplayName(plan.name);
  const price = plan.priceMonthly || plan.priceYearly || plan.priceQuarterly;
  const message = `Halo, saya tertarik dengan paket ${planName} (${price}) untuk Virtual Office di ${props.cityName || 'kota ini'}. Bisakah saya mendapatkan informasi lebih detail dan proses pendaftarannya?`;
  return `https://wa.me/6281234567890?text=${encodeURIComponent(message)}`;
};

// Get simplified plan name for display
const getPlanDisplayName = (fullName) => {
  if (fullName.includes('Basic')) return 'GESIT'
  if (fullName.includes('Virtual Office + PT')) return 'TERAMPIL'
  if (fullName.includes('Virtual Office + CV')) return 'HEBAT'
  if (fullName.includes('Paket Hemat')) return 'HEMAT'
  if (fullName.includes('Paket + PT')) return '+ PT'
  if (fullName.includes('Paket + CV')) return '+ CV'
  return fullName.split(' ')[0].toUpperCase()
}

// Format price for better display
const formatPrice = (price) => {
  if (!price) return ''
  // Remove 'IDR' and format the number
  return price.replace('IDR', 'Rp').replace(/\s+/g, ' ')
}

// Get features for each plan based on index
const getPlanFeatures = (planIndex) => {
  const basicFeatures = [
    'Alamat bisnis premium di lokasi strategis',
    'Nomor telepon kantor bersama',
    'Penerimaan surat dan paket',
    'Notifikasi penerimaan surat real-time'
  ];

  const standardFeatures = [
    'Alamat bisnis premium di lokasi strategis',
    'Nomor telepon kantor bersama',
    'Layanan call center profesional',
    'Surat kontrak sewa menyewa resmi',
    'Handling telepon incoming',
    'Penerimaan surat dan paket',
    'Notifikasi penerimaan surat real-time'
  ];

  const advancedFeatures = [
    'Layanan resepsionis profesional',
    'Penerimaan tamu di kantor',
    'Bantuan pengurusan perizinan usaha',
    'Dukungan pengurusan PKP',
    'Surat keterangan domisili usaha',
    'Jasa pengiriman surat'
  ];

  const premiumFeatures = [
    'Pinjam IMB untuk OSS',
    'Forward telepon ke nomor HP',
    'Meeting room (2 jam/hari)',
    'Free coworking space (3 hari/bulan)',
    'Diskon khusus sewa 2 tahun'
  ];

  const ptFeatures = [
    'Bantuan pendirian PT',
    'Konsultasi legalitas PT',
    'Dokumen pendirian PT',
    'Meeting room (1 jam/hari)'
  ];

  const cvFeatures = [
    'Bantuan pendirian CV',
    'Konsultasi legalitas CV',
    'Dokumen pendirian CV',
    'Meeting room (1 jam/hari)'
  ];

  // Plan 0 (GESIT) - Standard features
  if (planIndex === 0) {
    return standardFeatures;
  }

  // Plan 1 (TERAMPIL) - Standard + Advanced features
  if (planIndex === 1) {
    return [...standardFeatures, ...advancedFeatures, 'Meeting room (1 jam/hari)', 'Free coworking space (1 hari/bulan)'];
  }

  // Plan 2 (HEBAT) - All features
  if (planIndex === 2) {
    return [...standardFeatures, ...advancedFeatures, ...premiumFeatures];
  }

  // Plan 3 (HEMAT) - Basic features only
  if (planIndex === 3) {
    return basicFeatures;
  }

  // Plan 4 (+ PT) - Standard + PT features
  if (planIndex === 4) {
    return [...standardFeatures, ...ptFeatures];
  }

  // Plan 5 (+ CV) - Standard + CV features
  if (planIndex === 5) {
    return [...standardFeatures, ...cvFeatures];
  }

  return standardFeatures;
}
</script>
