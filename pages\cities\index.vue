<template>
  <div>
    <!-- Cities Hero -->
    <div class="relative">
      <div class="absolute inset-0 z-0">
        <img 
          src="https://images.pexels.com/photos/1098460/pexels-photo-1098460.jpeg" 
          alt="Virtual Office Locations" 
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-r from-primary-900/90 to-black/70"></div>
      </div>
      
      <div class="relative z-10 pt-32 pb-20">
        <div class="container mx-auto px-4">
          <div class="max-w-3xl animate-slide-up">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
              Our Virtual Office Locations
            </h1>
            <p class="text-xl text-gray-200 mb-6">
              Prestigious business addresses in major Indonesian cities
            </p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Cities Grid -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <CityCard v-for="city in cities" :key="city.id" :city="city" />
        </div>
      </div>
    </section>
    
    <!-- Compare Plans -->
    <!-- <section class="py-16">
      <div class="container mx-auto px-4">
        <SectionHeading 
          title="Compare Our Plans"
          subtitle="Find the perfect virtual office solution for your business needs"
        />
        
        <div class="overflow-x-auto">
          <table class="w-full bg-white rounded-xl shadow-md">
            <thead>
              <tr>
                <th class="px-6 py-4 text-left bg-gray-50 rounded-tl-xl">Features</th>
                <th class="px-6 py-4 text-center">Basic</th>
                <th class="px-6 py-4 text-center">Standard</th>
                <th class="px-6 py-4 text-center bg-primary-50 rounded-tr-xl">Premium</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(feature, index) in planFeatures" :key="index" 
                  :class="{'bg-gray-50': index % 2 === 0, 'rounded-bl-xl': index === planFeatures.length - 1}">
                <td class="px-6 py-4 font-medium">{{ feature.name }}</td>
                <td class="px-6 py-4 text-center">
                  <Icon v-if="feature.basic" name="heroicons:check" class="text-green-500 inline-block" />
                  <Icon v-else name="heroicons:x-mark" class="text-red-500 inline-block" />
                </td>
                <td class="px-6 py-4 text-center">
                  <Icon v-if="feature.standard" name="heroicons:check" class="text-green-500 inline-block" />
                  <Icon v-else name="heroicons:x-mark" class="text-red-500 inline-block" />
                </td>
                <td class="px-6 py-4 text-center bg-primary-50">
                  <Icon v-if="feature.premium" name="heroicons:check" class="text-green-500 inline-block" />
                  <Icon v-else name="heroicons:x-mark" class="text-red-500 inline-block" />
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 font-medium rounded-bl-xl">Price Range</td>
                <td class="px-6 py-4 text-center">Rp 800K - 1.2M</td>
                <td class="px-6 py-4 text-center">Rp 1.2M - 1.8M</td>
                <td class="px-6 py-4 text-center bg-primary-50 rounded-br-xl">Rp 1.8M - 2.5M</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="mt-8 text-center">
          <AppButton size="lg">Contact For Custom Plans</AppButton>
        </div>
      </div>
    </section> -->
  </div>
</template>

<script setup>
const { data: citiesData } = await useFetch('/api/cities')
const cities = computed(() => citiesData.value?.cities || [])

const planFeatures = [
  { name: 'Business Address', basic: true, standard: true, premium: true },
  { name: 'Mail Handling', basic: true, standard: true, premium: true },
  { name: 'Mail Forwarding', basic: false, standard: true, premium: true },
  { name: 'Call Answering', basic: false, standard: true, premium: true },
  { name: 'Meeting Room Hours', basic: '2 hrs/month', standard: '8 hrs/month', premium: '20 hrs/month' },
  { name: 'Virtual Assistant', basic: false, standard: false, premium: true },
  { name: 'Business Lounge Access', basic: false, standard: true, premium: true },
  { name: 'Company Formation Support', basic: false, standard: false, premium: true },
]

useHead({
  title: 'Virtual Office Locations | Gemilang VO',
  meta: [
    { name: 'description', content: 'Explore our virtual office locations across Indonesia. Premium business addresses in Yogyakarta, Semarang, Surakarta, Tangerang, and Surabaya.' }
  ]
})
</script>